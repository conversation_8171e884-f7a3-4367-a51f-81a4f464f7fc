import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_profile_model.dart';
import '../models/user_model.dart';
import '../models/school_model.dart';

/// Abstract interface for schools local data source
abstract class SchoolsLocalDataSource {
  /// Store user profile data
  Future<void> storeUserProfile(UserProfileModel userProfile);
  
  /// Get stored user profile data
  Future<UserProfileModel?> getUserProfile();
  
  /// Store user data
  Future<void> storeUser(UserModel user);
  
  /// Get stored user data
  Future<UserModel?> getUser();
  
  /// Store school data
  Future<void> storeSchool(SchoolModel school);
  
  /// Get stored school data
  Future<SchoolModel?> getSchool();
  
  
  /// Clear all stored data
  Future<void> clearAllData();
  
  /// Check if user is authenticated
  Future<bool> isUserAuthenticated();

  /// Store user photo
  Future<void> storeUserPhotoBase64(String photo);
  
  /// Get stored user photo
  Future<String?> getUserPhotoBase64();
}

/// Implementation of SchoolsLocalDataSource
class SchoolsLocalDataSourceImpl implements SchoolsLocalDataSource {
  final SharedPreferences sharedPreferences;
  
  // Storage keys
  static const String _userProfileKey = 'user_profile';
  static const String profilePhotoKey = "user_photo";
  static const String _userKey = 'user_data';
  static const String _schoolKey = 'school_data';
  static const String _userSchoolsKey = 'user_schools_list';
  static const String _isAuthenticatedKey = 'is_authenticated';

  SchoolsLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<void> storeUserProfile(UserProfileModel userProfile) async {
    final userProfileJson = jsonEncode(userProfile.toJson());
    final userPhotoBase64 = userProfile.photo;
    await storeUserPhotoBase64(userPhotoBase64);
    await sharedPreferences.setString(_userProfileKey, userProfileJson);
    
    // Also store authentication status
    await sharedPreferences.setBool(_isAuthenticatedKey, userProfile.isAuthenticated ?? false);
  }


  @override
  Future<String?> getUserPhotoBase64() async {
    return sharedPreferences.getString(profilePhotoKey);
  }


  @override
  Future<void> storeUserPhotoBase64(String photo) async {
    await sharedPreferences.setString(profilePhotoKey, photo);
  }

  @override
  Future<UserProfileModel?> getUserProfile() async {
    final userProfileJson = sharedPreferences.getString(_userProfileKey);
    if (userProfileJson != null) {
      final userProfileMap = jsonDecode(userProfileJson) as Map<String, dynamic>;
      return UserProfileModel.fromJson(userProfileMap);
    }
    return null;
  }

  @override
  Future<void> storeUser(UserModel user) async {
    final userJson = jsonEncode(user.toJson());
    await sharedPreferences.setString(_userKey, userJson);
  }

  @override
  Future<UserModel?> getUser() async {
    final userJson = sharedPreferences.getString(_userKey);
    if (userJson != null) {
      final userMap = jsonDecode(userJson) as Map<String, dynamic>;
      return UserModel.fromJson(userMap);
    }
    return null;
  }

  @override
  Future<void> storeSchool(SchoolModel school) async {
    final schoolJson = jsonEncode(school.toJson());
    await sharedPreferences.setString(_schoolKey, schoolJson);
  }

  @override
  Future<SchoolModel?> getSchool() async {
    final schoolJson = sharedPreferences.getString(_schoolKey);
    if (schoolJson != null) {
      final schoolMap = jsonDecode(schoolJson) as Map<String, dynamic>;
      return SchoolModel.fromJson(schoolMap);
    }
    return null;
  }


  @override
  Future<void> clearAllData() async {
    await sharedPreferences.remove(_userProfileKey);
    await sharedPreferences.remove(_userKey);
    await sharedPreferences.remove(_schoolKey);
    await sharedPreferences.remove(_userSchoolsKey);
    await sharedPreferences.remove(_isAuthenticatedKey);
  }

  @override
  Future<bool> isUserAuthenticated() async {
    return sharedPreferences.getBool(_isAuthenticatedKey) ?? false;
  }
}
